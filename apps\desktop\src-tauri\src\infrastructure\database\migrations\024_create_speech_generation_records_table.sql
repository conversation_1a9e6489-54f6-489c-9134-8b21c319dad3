-- 创建语音生成记录表
CREATE TABLE IF NOT EXISTS speech_generation_records (
    id TEXT PRIMARY KEY,
    text TEXT NOT NULL,
    voice_id TEXT NOT NULL,
    voice_name TEXT,
    speed REAL NOT NULL DEFAULT 1.0,
    volume REAL NOT NULL DEFAULT 1.0,
    emotion TEXT,
    audio_url TEXT,
    local_file_path TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    error_message TEXT,
    created_at TEXT NOT NULL,
    started_at TEXT,
    completed_at TEXT,
    duration_ms INTEGER
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_speech_generation_records_voice_id ON speech_generation_records(voice_id);
CREATE INDEX IF NOT EXISTS idx_speech_generation_records_created_at ON speech_generation_records(created_at);
CREATE INDEX IF NOT EXISTS idx_speech_generation_records_status ON speech_generation_records(status);
