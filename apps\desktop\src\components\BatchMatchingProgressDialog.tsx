/**
 * 一键匹配进度对话框组件
 * 遵循前端开发规范的组件设计原则
 */

import React from 'react';
import {
  X,
  Loader2,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  AlertCircle,
} from 'lucide-react';
import {
  BatchMatchingProgress,
  BatchMatchingProgressStatus,
  getBatchMatchingProgressStatusDisplay,
  formatDuration,
} from '../types/batchMatching';

interface BatchMatchingProgressDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCancel?: () => void;
  progress: BatchMatchingProgress | null;
  canCancel?: boolean;
}

export const BatchMatchingProgressDialog: React.FC<BatchMatchingProgressDialogProps> = ({
  isOpen,
  onClose,
  onCancel,
  progress,
  canCancel = true,
}) => {
  if (!isOpen || !progress) return null;

  const getStatusIcon = () => {
    switch (progress.status) {
      case BatchMatchingProgressStatus.InProgress:
        return <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />;
      case BatchMatchingProgressStatus.Completed:
        return <CheckCircle className="w-8 h-8 text-green-500" />;
      case BatchMatchingProgressStatus.Failed:
        return <XCircle className="w-8 h-8 text-red-500" />;
      case BatchMatchingProgressStatus.Cancelled:
        return <AlertCircle className="w-8 h-8 text-yellow-500" />;
      default:
        return <Clock className="w-8 h-8 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (progress.status) {
      case BatchMatchingProgressStatus.InProgress:
        return 'text-blue-600';
      case BatchMatchingProgressStatus.Completed:
        return 'text-green-600';
      case BatchMatchingProgressStatus.Failed:
        return 'text-red-600';
      case BatchMatchingProgressStatus.Cancelled:
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  const progressPercentage = progress.total_bindings > 0 
    ? Math.round((progress.current_binding_index / progress.total_bindings) * 100)
    : 0;

  const isInProgress = progress.status === BatchMatchingProgressStatus.InProgress;
  const isCompleted = progress.status === BatchMatchingProgressStatus.Completed;
  const isFailed = progress.status === BatchMatchingProgressStatus.Failed;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div>
              <h2 className={`text-xl font-semibold ${getStatusColor()}`}>
                {getBatchMatchingProgressStatusDisplay(progress.status)}
              </h2>
              <p className="text-sm text-gray-600">
                一键匹配进度
              </p>
            </div>
          </div>
          {(isCompleted || isFailed) && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          )}
        </div>

        {/* 内容 */}
        <div className="p-6 space-y-6">
          {/* 进度条 */}
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>总进度</span>
              <span>{progress.current_binding_index} / {progress.total_bindings}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  isCompleted ? 'bg-green-500' : 
                  isFailed ? 'bg-red-500' : 
                  'bg-blue-500'
                }`}
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            <div className="text-center text-sm text-gray-600 mt-1">
              {progressPercentage}%
            </div>
          </div>

          {/* 当前状态 */}
          {isInProgress && progress.current_template_name && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">正在匹配</span>
              </div>
              <p className="text-sm text-blue-700 mt-1">
                {progress.current_template_name}
              </p>
            </div>
          )}

          {/* 统计信息 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">已完成</span>
              </div>
              <p className="text-lg font-bold text-green-900">{progress.completed_bindings}</p>
            </div>
            
            <div className="bg-red-50 p-3 rounded-lg">
              <div className="flex items-center space-x-2">
                <XCircle className="w-4 h-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">失败</span>
              </div>
              <p className="text-lg font-bold text-red-900">{progress.failed_bindings}</p>
            </div>
          </div>

          {/* 时间信息 */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">已用时间:</span>
                <p className="font-medium">{formatDuration(progress.elapsed_time_ms)}</p>
              </div>
              {progress.estimated_remaining_ms && isInProgress && (
                <div>
                  <span className="text-gray-600">预计剩余:</span>
                  <p className="font-medium">{formatDuration(progress.estimated_remaining_ms)}</p>
                </div>
              )}
            </div>
          </div>

          {/* 完成状态消息 */}
          {isCompleted && (
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium text-green-800">匹配完成</span>
              </div>
              <p className="text-sm text-green-700 mt-1">
                所有模板匹配已完成，点击关闭查看详细结果。
              </p>
            </div>
          )}

          {/* 失败状态消息 */}
          {isFailed && (
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <XCircle className="w-5 h-5 text-red-600" />
                <span className="text-sm font-medium text-red-800">匹配失败</span>
              </div>
              <p className="text-sm text-red-700 mt-1">
                匹配过程中发生错误，请检查项目配置后重试。
              </p>
            </div>
          )}
        </div>

        {/* 底部 */}
        <div className="flex justify-end p-6 border-t border-gray-200 space-x-3">
          {isInProgress && canCancel && onCancel && (
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              取消匹配
            </button>
          )}
          {(isCompleted || isFailed) && (
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              查看结果
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
